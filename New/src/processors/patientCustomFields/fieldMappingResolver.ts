/**
 * Field Mapping Resolver
 * 
 * Provides database operations for custom field mappings between
 * AutoPatient and CliniCore platforms. Handles CRUD operations
 * and mapping resolution for the custom fields system.
 * 
 * @fileoverview Field mapping database operations
 * @version 1.0.0
 * @since 2024-08-07
 */

import { eq } from "drizzle-orm";
import { db } from "@/database";
import { customFields } from "@/database/schema";
import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logError, logInfo } from "@/utils/logger";

// ============================================================================
// TYPES
// ============================================================================

/**
 * Field mapping from database
 */
export interface FieldMappingRecord {
	id: string;
	apId: string | null;
	ccId: number | null;
	name: string | null;
	label: string | null;
	type: string | null;
	apConfig: APGetCustomFieldType | null;
	ccConfig: GetCCCustomField | null;
	mappingType: string;
	apStandardField: string | null;
	ccStandardField: string | null;
	createdAt: Date;
	updatedAt: Date;
}

/**
 * Simplified field mapping for v2 system
 */
export interface SimpleFieldMapping {
	id: string;
	apId: string;
	ccId: string;
	apConfig: APGetCustomFieldType;
	ccConfig: GetCCCustomField;
	mappingType: string;
}

// ============================================================================
// DATABASE OPERATIONS
// ============================================================================

/**
 * Get all field mappings from database
 * 
 * Retrieves all custom field mappings stored in the database.
 * Used by the v2 custom field system for synchronization operations.
 * 
 * @returns Array of field mappings
 */
export async function getAllFieldMappings(): Promise<SimpleFieldMapping[]> {
	try {
		logDebug("Fetching all field mappings from database");
		
		const mappings = await db
			.select()
			.from(customFields)
			.where(eq(customFields.apId, customFields.apId)); // Get all records
		
		// Filter and transform mappings to ensure required fields exist
		const validMappings = mappings
			.filter((mapping): mapping is FieldMappingRecord & {
				apId: string;
				ccId: number;
				apConfig: APGetCustomFieldType;
				ccConfig: GetCCCustomField;
			} => {
				return (
					mapping.apId !== null &&
					mapping.ccId !== null &&
					mapping.apConfig !== null &&
					mapping.ccConfig !== null
				);
			})
			.map((mapping): SimpleFieldMapping => ({
				id: mapping.id,
				apId: mapping.apId,
				ccId: mapping.ccId.toString(),
				apConfig: mapping.apConfig,
				ccConfig: mapping.ccConfig,
				mappingType: mapping.mappingType || "custom_to_custom"
			}));
		
		logInfo(`Retrieved ${validMappings.length} valid field mappings from database`);
		return validMappings;
		
	} catch (error) {
		logError("Failed to fetch field mappings from database", { error: String(error) });
		return [];
	}
}

/**
 * Get field mapping by AP field ID
 * 
 * @param apFieldId - AutoPatient field ID
 * @returns Field mapping if found
 */
export async function getFieldMappingByApId(apFieldId: string): Promise<SimpleFieldMapping | null> {
	try {
		logDebug("Fetching field mapping by AP ID", { apFieldId });
		
		const mapping = await db
			.select()
			.from(customFields)
			.where(eq(customFields.apId, apFieldId))
			.limit(1);
		
		if (mapping.length === 0) {
			return null;
		}
		
		const record = mapping[0];
		if (!record.apId || !record.ccId || !record.apConfig || !record.ccConfig) {
			logError("Invalid field mapping record - missing required fields", { 
				recordId: record.id,
				apId: record.apId,
				ccId: record.ccId
			});
			return null;
		}
		
		return {
			id: record.id,
			apId: record.apId,
			ccId: record.ccId.toString(),
			apConfig: record.apConfig,
			ccConfig: record.ccConfig,
			mappingType: record.mappingType || "custom_to_custom"
		};
		
	} catch (error) {
		logError("Failed to fetch field mapping by AP ID", { 
			apFieldId, 
			error: String(error) 
		});
		return null;
	}
}

/**
 * Get field mapping by CC field ID
 * 
 * @param ccFieldId - CliniCore field ID
 * @returns Field mapping if found
 */
export async function getFieldMappingByCcId(ccFieldId: number): Promise<SimpleFieldMapping | null> {
	try {
		logDebug("Fetching field mapping by CC ID", { ccFieldId });
		
		const mapping = await db
			.select()
			.from(customFields)
			.where(eq(customFields.ccId, ccFieldId))
			.limit(1);
		
		if (mapping.length === 0) {
			return null;
		}
		
		const record = mapping[0];
		if (!record.apId || !record.ccId || !record.apConfig || !record.ccConfig) {
			logError("Invalid field mapping record - missing required fields", { 
				recordId: record.id,
				apId: record.apId,
				ccId: record.ccId
			});
			return null;
		}
		
		return {
			id: record.id,
			apId: record.apId,
			ccId: record.ccId.toString(),
			apConfig: record.apConfig,
			ccConfig: record.ccConfig,
			mappingType: record.mappingType || "custom_to_custom"
		};
		
	} catch (error) {
		logError("Failed to fetch field mapping by CC ID", { 
			ccFieldId, 
			error: String(error) 
		});
		return null;
	}
}

/**
 * Create or update field mapping
 * 
 * @param mapping - Field mapping to save
 * @returns Success status
 */
export async function saveFieldMapping(mapping: {
	apId: string;
	ccId: number;
	apConfig: APGetCustomFieldType;
	ccConfig: GetCCCustomField;
	mappingType?: string;
}): Promise<boolean> {
	try {
		logDebug("Saving field mapping", { 
			apId: mapping.apId, 
			ccId: mapping.ccId 
		});
		
		// Check if mapping already exists
		const existing = await getFieldMappingByApId(mapping.apId);
		
		if (existing) {
			// Update existing mapping
			await db
				.update(customFields)
				.set({
					ccId: mapping.ccId,
					apConfig: mapping.apConfig,
					ccConfig: mapping.ccConfig,
					mappingType: mapping.mappingType || "custom_to_custom",
					name: mapping.apConfig.name,
					label: mapping.ccConfig.label || mapping.ccConfig.name,
					type: mapping.ccConfig.type,
					updatedAt: new Date()
				})
				.where(eq(customFields.apId, mapping.apId));
			
			logInfo("Updated existing field mapping", { 
				apId: mapping.apId, 
				ccId: mapping.ccId 
			});
		} else {
			// Create new mapping
			await db
				.insert(customFields)
				.values({
					id: `mapping-${mapping.apId}-${mapping.ccId}`,
					apId: mapping.apId,
					ccId: mapping.ccId,
					apConfig: mapping.apConfig,
					ccConfig: mapping.ccConfig,
					mappingType: mapping.mappingType || "custom_to_custom",
					name: mapping.apConfig.name,
					label: mapping.ccConfig.label || mapping.ccConfig.name,
					type: mapping.ccConfig.type,
					createdAt: new Date(),
					updatedAt: new Date()
				});
			
			logInfo("Created new field mapping", { 
				apId: mapping.apId, 
				ccId: mapping.ccId 
			});
		}
		
		return true;
		
	} catch (error) {
		logError("Failed to save field mapping", { 
			apId: mapping.apId, 
			ccId: mapping.ccId,
			error: String(error) 
		});
		return false;
	}
}
