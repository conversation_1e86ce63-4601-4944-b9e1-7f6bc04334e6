/**
 * Patient Custom Fields Types
 * 
 * Type definitions for patient custom field synchronization operations.
 * 
 * @fileoverview Patient custom fields type definitions
 * @version 1.0.0
 * @since 2024-08-07
 */

// ============================================================================
// PLATFORM TYPES
// ============================================================================

/**
 * Supported platforms
 */
export type Platform = "ap" | "cc";

// ============================================================================
// SYNC RESULT TYPES
// ============================================================================

/**
 * Patient custom field synchronization result
 */
export interface PatientCustomFieldSyncResult {
	success: boolean;
	patientId: string;
	targetPlatform: Platform;
	fieldsProcessed: number;
	fieldsUpdated: number;
	fieldsSkipped: number;
	fieldsFailed: number;
	executionTimeMs: number;
	results: Array<{
		fieldName: string;
		success: boolean;
		error?: string;
	}>;
	errors: string[];
	warnings: string[];
}

// ============================================================================
// FIELD VALUE TYPES
// ============================================================================

/**
 * Custom field value for synchronization
 */
export interface CustomFieldValue {
	fieldId: string;
	fieldName: string;
	value: unknown;
	platform: Platform;
}

/**
 * Field synchronization context
 */
export interface FieldSyncContext {
	patientId: string;
	sourcePlatform: Platform;
	targetPlatform: Platform;
	requestId: string;
	dryRun: boolean;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

export type {
	Platform as default
};
