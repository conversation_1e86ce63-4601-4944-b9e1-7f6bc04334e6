/**
 * Field Type Converter for Custom Fields v2
 *
 * Provides bidirectional field type conversion between AutoPatient (AP) and CliniCore (CC)
 * platforms based on the complete mapping matrix defined in DATA-TYPE-MAP.md.
 *
 * **Key Features:**
 * - Complete bidirectional support (CC ↔ AP)
 * - Priority-based multi-value field handling (allowMultipleValues overrides base type)
 * - Medical field fallback logic
 * - TEXTBOX_LIST Record<string, string> structure support
 * - Fallback conversion (CC multi-value → AP TEXT with | separator)
 * - Unknown field type handling with multi-value awareness
 * - Comprehensive error handling and logging
 *
 * **CC → AP Conversion Priority:**
 * 1. Multi-value fields (allowMultipleValues: true):
 *    - CC select/select-or-custom → AP MULTIPLE_OPTIONS
 *    - All other CC field types → AP TEXTBOX_LIST
 * 2. Medical field types → TEXT fallback
 * 3. Boolean fields → RADIO
 * 4. Select fields → SINGLE_OPTIONS
 * 5. Standard mappings + unknown field fallback → TEXT
 *
 * @fileoverview Bidirectional field type conversion system
 * @version 2.1.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCC<PERSON>ustomField } from "@type";
import type { ConvertedFieldType } from "../types";
import { Platform } from "../types";

// ============================================================================
// FIELD TYPE MAPPING CONSTANTS
// ============================================================================

/**
 * AP to CC field type mappings from DATA-TYPE-MAP.md
 */
const AP_TO_CC_MAPPINGS: Record<string, string> = {
	TEXT: "text",
	LARGE_TEXT: "textarea",
	NUMERICAL: "number",
	PHONE: "telephone",
	MONETORY: "text", // Monetary values stored as text in CC
	CHECKBOX: "select", // Multi-select checkbox mapping
	SINGLE_OPTIONS: "select",
	MULTIPLE_OPTIONS: "select", // Multi-select dropdown mapping
	DATE: "date",
	RADIO: "select", // Default to select, special case for boolean handled separately
	EMAIL: "email",
	TEXTBOX_LIST: "text", // Multi-value text field mapping
	// FILE_UPLOAD is skipped - not supported in CC
};

/**
 * CC to AP field type mappings from DATA-TYPE-MAP.md
 * Note: Multi-value handling is determined by allowMultipleValues flag
 */
const CC_TO_AP_MAPPINGS: Record<string, string> = {
	text: "TEXT",
	textarea: "LARGE_TEXT", 
	number: "NUMERICAL",
	telephone: "PHONE",
	email: "EMAIL",
	date: "DATE",
	select: "SINGLE_OPTIONS", // Default to single, multi handled separately
	"select-or-custom": "SINGLE_OPTIONS",
	boolean: "RADIO",
};

/**
 * Medical field types that fallback to TEXT in AP
 */
const MEDICAL_FIELD_TYPES = [
	"medication",
	"permanent-diagnoses", 
	"patient-has-recommended"
];

/**
 * AP field types that support multi-value in CC
 */
const AP_MULTI_VALUE_TYPES = [
	"CHECKBOX",
	"MULTIPLE_OPTIONS",
	"TEXTBOX_LIST"
];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a CC field supports multiple values
 */
export function isMultiValueField(field: GetCCCustomField): boolean {
	return field.allowMultipleValues === true;
}

/**
 * Check if a CC field type is a medical field
 */
export function isMedicalField(ccFieldType: string): boolean {
	return MEDICAL_FIELD_TYPES.includes(ccFieldType);
}

/**
 * Check if an AP field type supports multiple values
 */
export function isApMultiValueType(apFieldType: string): boolean {
	return AP_MULTI_VALUE_TYPES.includes(apFieldType);
}

/**
 * Check if AP RADIO field is a boolean field (Yes/No values)
 */
export function isApBooleanRadio(apField: APGetCustomFieldType): boolean {
	if (apField.dataType !== "RADIO" || !apField.picklistOptions) {
		return false;
	}
	
	const options = Array.isArray(apField.picklistOptions) 
		? apField.picklistOptions 
		: [];
		
	// Check for Yes/Ja, No/Nein pattern
	const hasYesNo = options.some(opt => {
		const label = typeof opt === "string" ? opt : opt.label;
		return ["Yes", "Ja", "No", "Nein"].includes(label);
	});
	
	return hasYesNo && options.length <= 4; // Allow for Yes/Ja/No/Nein combinations
}

// ============================================================================
// MAIN CONVERSION FUNCTIONS
// ============================================================================

/**
 * Convert CC field to AP field type
 */
export function getApFieldTypeForCcField(ccField: GetCCCustomField): ConvertedFieldType {
	const conversionNotes: string[] = [];
	let targetType: string;
	let fallbackType: string | undefined;
	let requiresValueConversion = false;
	let valueConversionType: ConvertedFieldType["valueConversionType"];

	// PRIORITY 1: Handle multi-value fields first (overrides all other mappings)
	if (isMultiValueField(ccField)) {
		// Special case: CC select fields with multi-value support should map to AP MULTIPLE_OPTIONS
		if (ccField.type === "select" || ccField.type === "select-or-custom") {
			targetType = "MULTIPLE_OPTIONS";
			fallbackType = "TEXTBOX_LIST";
			conversionNotes.push(`Multi-value CC select field (${ccField.type}) converted to MULTIPLE_OPTIONS with TEXTBOX_LIST fallback`);
			requiresValueConversion = false; // Both are select-based, no conversion needed
			valueConversionType = undefined;
		} else {
			// For all other CC field types with allowMultipleValues: true, target should be TEXTBOX_LIST
			targetType = "TEXTBOX_LIST";
			fallbackType = "TEXT";
			conversionNotes.push(`Multi-value CC field (${ccField.type}) converted to TEXTBOX_LIST with TEXT fallback`);
			requiresValueConversion = true;
			valueConversionType = "textbox_list_object";
		}

		return {
			targetType,
			isMultiValue: true,
			fallbackType,
			conversionNotes,
			requiresValueConversion,
			valueConversionType
		};
	}

	// PRIORITY 2: Handle medical field fallbacks (only for single-value fields)
	if (isMedicalField(ccField.type)) {
		targetType = "TEXT";
		conversionNotes.push(`Medical field type '${ccField.type}' converted to TEXT fallback`);
		requiresValueConversion = true;
		valueConversionType = "direct_mapping";

		return {
			targetType,
			isMultiValue: false,
			conversionNotes,
			requiresValueConversion,
			valueConversionType
		};
	}

	// PRIORITY 3: Handle boolean fields
	if (ccField.type === "boolean") {
		targetType = "RADIO";
		conversionNotes.push("Boolean field converted to RADIO with Yes/Ja/No/Nein options");
		requiresValueConversion = true;
		valueConversionType = "boolean_conversion";

		return {
			targetType,
			isMultiValue: false,
			conversionNotes,
			requiresValueConversion,
			valueConversionType
		};
	}

	// PRIORITY 4: Handle select fields
	if (ccField.type === "select" || ccField.type === "select-or-custom") {
		targetType = "SINGLE_OPTIONS";
		conversionNotes.push(`Select field converted to ${targetType}`);

		return {
			targetType,
			isMultiValue: false,
			conversionNotes,
			requiresValueConversion: false
		};
	}

	// PRIORITY 5: Handle standard mappings and unknown field types
	targetType = CC_TO_AP_MAPPINGS[ccField.type];
	if (!targetType) {
		// Unknown field type fallback: default to TEXT
		// Note: Multi-value unknown fields are already handled in PRIORITY 1
		targetType = "TEXT";
		conversionNotes.push(`Unknown CC field type '${ccField.type}' converted to TEXT fallback`);
		requiresValueConversion = true;
		valueConversionType = "direct_mapping";
	}

	return {
		targetType,
		isMultiValue: false,
		conversionNotes,
		requiresValueConversion,
		valueConversionType
	};
}

/**
 * Convert AP field to CC field type
 */
export function getCcFieldTypeForApField(apField: APGetCustomFieldType): ConvertedFieldType {
	const conversionNotes: string[] = [];
	let targetType: string;
	let requiresValueConversion = false;
	let valueConversionType: ConvertedFieldType["valueConversionType"];
	
	// Handle FILE_UPLOAD - not supported in CC
	if (apField.dataType === "FILE_UPLOAD") {
		throw new Error("FILE_UPLOAD fields are not supported in CliniCore and should be skipped");
	}
	
	// Handle RADIO field - check if it's boolean
	if (apField.dataType === "RADIO") {
		if (isApBooleanRadio(apField)) {
			targetType = "boolean";
			conversionNotes.push("Boolean RADIO field converted to CC boolean");
			requiresValueConversion = true;
			valueConversionType = "boolean_conversion";
		} else {
			targetType = "select";
			conversionNotes.push("RADIO field converted to CC select");
		}
		
		return {
			targetType,
			isMultiValue: false,
			conversionNotes,
			requiresValueConversion,
			valueConversionType
		};
	}
	
	// Handle multi-value AP fields
	if (isApMultiValueType(apField.dataType)) {
		if (apField.dataType === "TEXTBOX_LIST") {
			targetType = "text";
		} else if (apField.dataType === "CHECKBOX" || apField.dataType === "MULTIPLE_OPTIONS") {
			targetType = "select";
		} else {
			targetType = "text"; // Fallback
		}
		
		conversionNotes.push(`Multi-value AP field ${apField.dataType} converted to CC ${targetType} with allowMultipleValues: true`);
		requiresValueConversion = true;
		valueConversionType = "textbox_list_object";
		
		return {
			targetType,
			isMultiValue: true,
			conversionNotes,
			requiresValueConversion,
			valueConversionType
		};
	}
	
	// Handle standard mappings
	targetType = AP_TO_CC_MAPPINGS[apField.dataType];
	if (!targetType) {
		targetType = "text";
		conversionNotes.push(`Unknown AP field type '${apField.dataType}' converted to text fallback`);
		requiresValueConversion = true;
		valueConversionType = "direct_mapping";
	}
	
	return {
		targetType,
		isMultiValue: false,
		conversionNotes,
		requiresValueConversion,
		valueConversionType
	};
}

/**
 * Main field type conversion function
 * 
 * Converts a field from one platform to the appropriate field type for the target platform.
 * Handles all special cases and provides detailed conversion metadata.
 * 
 * @param field - Source field (AP or CC format)
 * @param targetPlatform - Target platform ("ap" or "cc")
 * @returns Conversion result with target type and metadata
 */
export function convertFieldType(
	field: APGetCustomFieldType | GetCCCustomField,
	targetPlatform: Platform
): ConvertedFieldType {
	try {
		if (targetPlatform === Platform.AP) {
			// Converting CC field to AP field type
			return getApFieldTypeForCcField(field as GetCCCustomField);
		} else {
			// Converting AP field to CC field type
			return getCcFieldTypeForApField(field as APGetCustomFieldType);
		}
	} catch (error) {
		// Fallback conversion with error handling
		const fallbackType = targetPlatform === Platform.AP ? "TEXT" : "text";
		
		return {
			targetType: fallbackType,
			isMultiValue: false,
			conversionNotes: [`Conversion failed: ${String(error)}`, `Using fallback type: ${fallbackType}`],
			requiresValueConversion: true,
			valueConversionType: "direct_mapping"
		};
	}
}
