/**
 * Value Converter for Custom Fields v2
 *
 * Provides unified value conversion between AutoPatient (AP) and CliniCore (CC)
 * platforms with support for all field types and special handling for complex cases.
 *
 * **Key Features:**
 * - TEXTBOX_LIST Record<string, string> structure with option IDs as keys
 * - Multi-value → TEXT conversion with ` | ` separator (fallback only)
 * - Boolean conversion (true→"Yes", false→"No", AP RADIO options)
 * - Medical field value handling
 * - Type-aware conversion based on source/target field types
 * - Complete replacement for TEXTBOX_LIST (not append)
 * - Fresh data fetching for CC data instead of cached
 *
 * **Conversion Types:**
 * - `textbox_list_object`: TEXTBOX_LIST Record<string, string> handling
 * - `multi_value_to_text`: CC multi-value → AP TEXT with ` | ` separator
 * - `boolean_conversion`: Boolean ↔ RADIO conversion
 * - `direct_mapping`: Standard type-to-type conversion
 *
 * @fileoverview Unified value conversion system for custom fields
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type {
	ConversionResult,
	ValueConversionContext,
	ConvertedFieldType
} from "../types";
import { Platform } from "../types";

// ============================================================================
// CONVERSION CONSTANTS
// ============================================================================

/**
 * Separator for multi-value to TEXT conversion
 * Used when CC multi-value fields fallback to AP TEXT fields
 */
const MULTI_VALUE_TEXT_SEPARATOR = " | ";

/**
 * Boolean conversion mappings
 */
const BOOLEAN_MAPPINGS = {
	// CC boolean → AP RADIO
	CC_TO_AP: {
		true: "Yes",
		false: "No"
	},
	// AP RADIO → CC boolean
	AP_TO_CC: {
		"Yes": true,
		"Ja": true,
		"No": false,
		"Nein": false
	}
} as const;

/**
 * Default RADIO options for boolean fields
 */
const BOOLEAN_RADIO_OPTIONS = ["Yes", "Ja", "No", "Nein"];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Check if a value is empty or should be filtered out
 */
function isEmptyValue(value: any): boolean {
	if (value === null || value === undefined) return true;
	if (typeof value === "string" && value.trim() === "") return true;
	if (Array.isArray(value) && value.length === 0) return true;
	if (typeof value === "object" && Object.keys(value).length === 0) return true;
	return false;
}

/**
 * Filter out empty values from arrays and objects
 */
function filterEmptyValues(value: any): any {
	if (Array.isArray(value)) {
		return value.filter(v => !isEmptyValue(v));
	}
	
	if (typeof value === "object" && value !== null) {
		const filtered: Record<string, any> = {};
		for (const [key, val] of Object.entries(value)) {
			if (!isEmptyValue(val)) {
				filtered[key] = val;
			}
		}
		return filtered;
	}
	
	return value;
}

/**
 * Normalize array values to strings
 */
function normalizeArrayToStrings(values: any[]): string[] {
	return values
		.filter(v => !isEmptyValue(v))
		.map(v => typeof v === "string" ? v : String(v));
}

/**
 * Convert Record<string, string> to array of values
 */
function recordToArray(record: Record<string, string>): string[] {
	return Object.values(record).filter(v => !isEmptyValue(v));
}

/**
 * Convert array to Record<string, string> with numeric keys
 */
function arrayToRecord(values: string[]): Record<string, string> {
	const record: Record<string, string> = {};
	values.forEach((value, index) => {
		if (!isEmptyValue(value)) {
			record[String(index + 1)] = value;
		}
	});
	return record;
}

// ============================================================================
// TEXTBOX_LIST CONVERSION FUNCTIONS
// ============================================================================

/**
 * Convert CC multi-value data to AP TEXTBOX_LIST format
 * 
 * TEXTBOX_LIST uses field_value object with option IDs as keys:
 * { "1": "value1", "2": "value2" }
 */
function convertToTextboxList(
	ccValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		let values: string[] = [];
		
		// Handle different CC multi-value formats
		if (Array.isArray(ccValue)) {
			values = normalizeArrayToStrings(ccValue);
		} else if (typeof ccValue === "string") {
			// Split by separator if it's a concatenated string
			values = ccValue.split(MULTI_VALUE_TEXT_SEPARATOR)
				.map(v => v.trim())
				.filter(v => v.length > 0);
		} else if (typeof ccValue === "object" && ccValue !== null) {
			// Handle object format (Record<string, string>)
			values = recordToArray(ccValue);
		} else if (!isEmptyValue(ccValue)) {
			// Single value
			values = [String(ccValue)];
		}
		
		// Filter empty values
		values = values.filter(v => !isEmptyValue(v));
		
		if (values.length === 0) {
			return {
				success: true,
				convertedValue: {},
				notes: ["No valid values to convert to TEXTBOX_LIST"]
			};
		}
		
		// Convert to Record<string, string> format with option IDs as keys
		const textboxListValue = arrayToRecord(values);
		
		return {
			success: true,
			convertedValue: textboxListValue,
			notes: [`Converted ${values.length} values to TEXTBOX_LIST format`]
		};
		
	} catch (error) {
		return {
			success: false,
			convertedValue: {},
			error: `TEXTBOX_LIST conversion failed: ${String(error)}`,
			notes: ["Fallback to empty TEXTBOX_LIST object"]
		};
	}
}

/**
 * Convert AP TEXTBOX_LIST data to CC multi-value format
 */
function convertFromTextboxList(
	apValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		let values: string[] = [];
		
		if (typeof apValue === "object" && apValue !== null) {
			// Handle Record<string, string> format
			values = recordToArray(apValue);
		} else if (Array.isArray(apValue)) {
			values = normalizeArrayToStrings(apValue);
		} else if (typeof apValue === "string") {
			// Handle comma-separated or pipe-separated values
			values = apValue.split(/[,|]/)
				.map(v => v.trim())
				.filter(v => v.length > 0);
		} else if (!isEmptyValue(apValue)) {
			values = [String(apValue)];
		}
		
		// Filter empty values
		values = values.filter(v => !isEmptyValue(v));
		
		if (values.length === 0) {
			return {
				success: true,
				convertedValue: [],
				notes: ["No valid values to convert from TEXTBOX_LIST"]
			};
		}
		
		return {
			success: true,
			convertedValue: values,
			notes: [`Converted ${values.length} values from TEXTBOX_LIST format`]
		};
		
	} catch (error) {
		return {
			success: false,
			convertedValue: [],
			error: `TEXTBOX_LIST conversion failed: ${String(error)}`,
			notes: ["Fallback to empty array"]
		};
	}
}

// ============================================================================
// MULTI-VALUE TO TEXT CONVERSION
// ============================================================================

/**
 * Convert CC multi-value to AP TEXT with pipe separator
 * This is a fallback when no TEXTBOX_LIST match is available
 */
function convertMultiValueToText(
	ccValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		let values: string[] = [];
		
		if (Array.isArray(ccValue)) {
			values = normalizeArrayToStrings(ccValue);
		} else if (typeof ccValue === "object" && ccValue !== null) {
			values = recordToArray(ccValue);
		} else if (!isEmptyValue(ccValue)) {
			values = [String(ccValue)];
		}
		
		// Filter empty values
		values = values.filter(v => !isEmptyValue(v));
		
		if (values.length === 0) {
			return {
				success: true,
				convertedValue: "",
				notes: ["No valid values to convert to TEXT"]
			};
		}
		
		// Join with pipe separator
		const textValue = values.join(MULTI_VALUE_TEXT_SEPARATOR);
		
		return {
			success: true,
			convertedValue: textValue,
			notes: [`Converted ${values.length} values to TEXT with pipe separator`]
		};
		
	} catch (error) {
		return {
			success: false,
			convertedValue: "",
			error: `Multi-value to TEXT conversion failed: ${String(error)}`,
			notes: ["Fallback to empty string"]
		};
	}
}

/**
 * Convert AP TEXT with pipe separator to CC multi-value
 */
function convertTextToMultiValue(
	apValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		if (isEmptyValue(apValue)) {
			return {
				success: true,
				convertedValue: [],
				notes: ["Empty TEXT value converted to empty array"]
			};
		}
		
		const textValue = String(apValue);
		
		// Split by pipe separator
		const values = textValue.split(MULTI_VALUE_TEXT_SEPARATOR)
			.map(v => v.trim())
			.filter(v => v.length > 0);
		
		return {
			success: true,
			convertedValue: values,
			notes: [`Converted TEXT to ${values.length} values using pipe separator`]
		};
		
	} catch (error) {
		return {
			success: false,
			convertedValue: [],
			error: `TEXT to multi-value conversion failed: ${String(error)}`,
			notes: ["Fallback to empty array"]
		};
	}
}

// ============================================================================
// BOOLEAN CONVERSION FUNCTIONS
// ============================================================================

/**
 * Convert CC boolean to AP RADIO value
 */
function convertBooleanToRadio(
	ccValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		if (isEmptyValue(ccValue)) {
			return {
				success: true,
				convertedValue: "",
				notes: ["Empty boolean value converted to empty string"]
			};
		}

		// Handle boolean values
		if (typeof ccValue === "boolean") {
			const radioValue = BOOLEAN_MAPPINGS.CC_TO_AP[ccValue];
			return {
				success: true,
				convertedValue: radioValue,
				notes: [`Boolean ${ccValue} converted to RADIO "${radioValue}"`]
			};
		}

		// Handle string representations
		const stringValue = String(ccValue).toLowerCase();
		if (stringValue === "true" || stringValue === "1") {
			return {
				success: true,
				convertedValue: BOOLEAN_MAPPINGS.CC_TO_AP[true],
				notes: [`String "${ccValue}" converted to RADIO "Yes"`]
			};
		} else if (stringValue === "false" || stringValue === "0") {
			return {
				success: true,
				convertedValue: BOOLEAN_MAPPINGS.CC_TO_AP[false],
				notes: [`String "${ccValue}" converted to RADIO "No"`]
			};
		}

		// Fallback for unknown values
		return {
			success: false,
			convertedValue: "",
			error: `Unknown boolean value: ${ccValue}`,
			notes: ["Fallback to empty string"]
		};

	} catch (error) {
		return {
			success: false,
			convertedValue: "",
			error: `Boolean to RADIO conversion failed: ${String(error)}`,
			notes: ["Fallback to empty string"]
		};
	}
}

/**
 * Convert AP RADIO to CC boolean value
 */
function convertRadioToBoolean(
	apValue: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		if (isEmptyValue(apValue)) {
			return {
				success: true,
				convertedValue: null,
				notes: ["Empty RADIO value converted to null"]
			};
		}

		const stringValue = String(apValue);
		const booleanValue = BOOLEAN_MAPPINGS.AP_TO_CC[stringValue as keyof typeof BOOLEAN_MAPPINGS.AP_TO_CC];

		if (booleanValue !== undefined) {
			return {
				success: true,
				convertedValue: booleanValue,
				notes: [`RADIO "${stringValue}" converted to boolean ${booleanValue}`]
			};
		}

		// Fallback for unknown values
		return {
			success: false,
			convertedValue: null,
			error: `Unknown RADIO value: ${apValue}`,
			notes: ["Fallback to null"]
		};

	} catch (error) {
		return {
			success: false,
			convertedValue: null,
			error: `RADIO to boolean conversion failed: ${String(error)}`,
			notes: ["Fallback to null"]
		};
	}
}

// ============================================================================
// DIRECT MAPPING FUNCTIONS
// ============================================================================

/**
 * Direct value mapping for standard field types
 */
function convertDirectMapping(
	value: any,
	context: ValueConversionContext
): ConversionResult {
	try {
		if (isEmptyValue(value)) {
			return {
				success: true,
				convertedValue: null,
				notes: ["Empty value converted to null"]
			};
		}

		// For direct mapping, we typically just pass the value through
		// with minimal transformation based on target field type
		let convertedValue = value;
		const notes: string[] = [];

		// Handle type-specific conversions
		if (context.targetField) {
			const targetType = "dataType" in context.targetField
				? context.targetField.dataType
				: context.targetField.type;

			switch (targetType) {
				case "NUMERICAL":
				case "number":
					// Convert to number if possible
					const numValue = Number(value);
					if (!isNaN(numValue)) {
						convertedValue = numValue;
						notes.push(`Converted to number: ${numValue}`);
					} else {
						convertedValue = String(value);
						notes.push(`Could not convert to number, using string: ${value}`);
					}
					break;

				case "TEXT":
				case "LARGE_TEXT":
				case "text":
				case "textarea":
				case "EMAIL":
				case "email":
				case "PHONE":
				case "telephone":
				case "DATE":
				case "date":
					// Convert to string
					convertedValue = String(value);
					notes.push(`Converted to string: ${convertedValue}`);
					break;

				default:
					// Keep as-is for unknown types
					notes.push(`Direct mapping for type ${targetType}`);
					break;
			}
		}

		return {
			success: true,
			convertedValue,
			notes
		};

	} catch (error) {
		return {
			success: false,
			convertedValue: value,
			error: `Direct mapping conversion failed: ${String(error)}`,
			notes: ["Fallback to original value"]
		};
	}
}

// ============================================================================
// MAIN CONVERSION FUNCTION
// ============================================================================

/**
 * Main value conversion function
 *
 * Converts field values between platforms based on the conversion type
 * identified by the field type converter.
 *
 * @param value - Source value to convert
 * @param conversionType - Type of conversion to perform
 * @param context - Conversion context with field information
 * @returns Conversion result with converted value and metadata
 */
export function convertFieldValue(
	value: any,
	conversionType: ConvertedFieldType["valueConversionType"],
	context: ValueConversionContext
): ConversionResult {
	// Handle undefined conversion type (no conversion needed)
	if (!conversionType) {
		return {
			success: true,
			convertedValue: value,
			notes: ["No conversion required"]
		};
	}

	// Route to appropriate conversion function
	switch (conversionType) {
		case "textbox_list_object":
			// Determine direction based on context
			if (context.targetPlatform === Platform.AP) {
				return convertToTextboxList(value, context);
			} else {
				return convertFromTextboxList(value, context);
			}

		case "multi_value_to_text":
			// Determine direction based on context
			if (context.targetPlatform === Platform.AP) {
				return convertMultiValueToText(value, context);
			} else {
				return convertTextToMultiValue(value, context);
			}

		case "boolean_conversion":
			// Determine direction based on context
			if (context.targetPlatform === Platform.AP) {
				return convertBooleanToRadio(value, context);
			} else {
				return convertRadioToBoolean(value, context);
			}

		case "direct_mapping":
			return convertDirectMapping(value, context);

		default:
			return {
				success: false,
				convertedValue: value,
				error: `Unknown conversion type: ${conversionType}`,
				notes: ["Fallback to original value"]
			};
	}
}

// ============================================================================
// CONVENIENCE EXPORT FUNCTIONS
// ============================================================================

/**
 * Convert CC multi-value field to AP TEXTBOX_LIST
 */
export function convertCcMultiValueToTextboxList(
	ccValue: any,
	sourceField: GetCCCustomField,
	targetField: APGetCustomFieldType
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.CC,
		targetPlatform: Platform.AP
	};

	return convertToTextboxList(ccValue, context);
}

/**
 * Convert AP TEXTBOX_LIST to CC multi-value field
 */
export function convertTextboxListToCcMultiValue(
	apValue: any,
	sourceField: APGetCustomFieldType,
	targetField: GetCCCustomField
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.AP,
		targetPlatform: Platform.CC
	};

	return convertFromTextboxList(apValue, context);
}

/**
 * Convert CC multi-value to AP TEXT with pipe separator
 */
export function convertCcMultiValueToText(
	ccValue: any,
	sourceField: GetCCCustomField,
	targetField: APGetCustomFieldType
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.CC,
		targetPlatform: Platform.AP
	};

	return convertMultiValueToText(ccValue, context);
}

/**
 * Convert AP TEXT to CC multi-value using pipe separator
 */
export function convertTextToCcMultiValue(
	apValue: any,
	sourceField: APGetCustomFieldType,
	targetField: GetCCCustomField
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.AP,
		targetPlatform: Platform.CC
	};

	return convertTextToMultiValue(apValue, context);
}

/**
 * Convert CC boolean to AP RADIO
 */
export function convertCcBooleanToRadio(
	ccValue: any,
	sourceField: GetCCCustomField,
	targetField: APGetCustomFieldType
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.CC,
		targetPlatform: Platform.AP
	};

	return convertBooleanToRadio(ccValue, context);
}

/**
 * Convert AP RADIO to CC boolean
 */
export function convertRadioToCcBoolean(
	apValue: any,
	sourceField: APGetCustomFieldType,
	targetField: GetCCCustomField
): ConversionResult {
	const context: ValueConversionContext = {
		sourceField,
		targetField,
		sourcePlatform: Platform.AP,
		targetPlatform: Platform.CC
	};

	return convertRadioToBoolean(apValue, context);
}

/**
 * Get boolean RADIO options for AP field creation
 */
export function getBooleanRadioOptions(): string[] {
	return [...BOOLEAN_RADIO_OPTIONS];
}

/**
 * Get multi-value text separator
 */
export function getMultiValueTextSeparator(): string {
	return MULTI_VALUE_TEXT_SEPARATOR;
}
