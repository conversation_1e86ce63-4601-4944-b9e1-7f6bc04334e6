/**
 * Type Checker for Custom Fields v2
 *
 * Provides field type compatibility validation between AutoPatient (AP) and CliniCore (CC)
 * platforms based on the complete compatibility matrix from DATA-TYPE-MAP.md.
 *
 * **Key Features:**
 * - Real field type compatibility validation
 * - Multi-value support checking (allowMultipleValues flag)
 * - TEXTBOX_LIST compatibility validation
 * - Medical field type support
 * - Boolean field detection and validation
 * - Confidence scoring for compatibility matches
 * - Detailed compatibility reasoning
 *
 * **Compatibility Rules:**
 * - CC multi-value fields (allowMultipleValues: true) → AP TEXTBOX_LIST/MULTIPLE_OPTIONS
 * - CC select with multi-value → AP MULTIPLE_OPTIONS (preferred) or TEXTBOX_LIST
 * - CC boolean → AP RADIO with Yes/Ja/No/Nein options
 * - Medical field types → AP TEXT fallback
 * - Standard type mappings from DATA-TYPE-MAP.md
 *
 * @fileoverview Field type compatibility validation system
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type { CompatibilityResult } from "../types";
import { Platform } from "../types";
import {
	isMultiValueField,
	isMedicalField,
	isApMultiValueType,
	isApBooleanRadio
} from "./fieldTypeConverter";

// ============================================================================
// COMPATIBILITY CONSTANTS
// ============================================================================

/**
 * AP to CC compatibility matrix
 * Maps AP field types to compatible CC field types
 */
const AP_TO_CC_COMPATIBILITY: Record<string, string[]> = {
	TEXT: ["text", "textarea", "medication", "permanent-diagnoses", "patient-has-recommended"],
	LARGE_TEXT: ["textarea", "text"],
	NUMERICAL: ["number"],
	PHONE: ["telephone", "text"],
	MONETORY: ["text", "number"],
	CHECKBOX: ["select", "select-or-custom"], // Multi-select
	SINGLE_OPTIONS: ["select", "select-or-custom"],
	MULTIPLE_OPTIONS: ["select", "select-or-custom"], // Multi-select
	DATE: ["date"],
	RADIO: ["select", "select-or-custom", "boolean"], // Can be boolean or select
	EMAIL: ["email", "text"],
	TEXTBOX_LIST: ["text", "textarea", "select", "select-or-custom"], // Multi-value
	// FILE_UPLOAD is not supported in CC
};

/**
 * CC to AP compatibility matrix
 * Maps CC field types to compatible AP field types
 */
const CC_TO_AP_COMPATIBILITY: Record<string, string[]> = {
	text: ["TEXT", "LARGE_TEXT", "TEXTBOX_LIST"], // Can be multi-value
	textarea: ["LARGE_TEXT", "TEXT", "TEXTBOX_LIST"], // Can be multi-value
	number: ["NUMERICAL", "MONETORY"],
	telephone: ["PHONE", "TEXT"],
	email: ["EMAIL", "TEXT"],
	date: ["DATE"],
	select: ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "CHECKBOX", "RADIO"], // Can be multi-value
	"select-or-custom": ["SINGLE_OPTIONS", "MULTIPLE_OPTIONS", "CHECKBOX", "RADIO"],
	boolean: ["RADIO"],
	// Medical field types
	medication: ["TEXT", "LARGE_TEXT"],
	"permanent-diagnoses": ["TEXT", "LARGE_TEXT"],
	"patient-has-recommended": ["TEXT", "LARGE_TEXT"],
};

/**
 * High compatibility field type pairs
 * These combinations have very high compatibility scores
 */
const HIGH_COMPATIBILITY_PAIRS: Array<[string, string]> = [
	// Exact matches
	["TEXT", "text"],
	["LARGE_TEXT", "textarea"],
	["NUMERICAL", "number"],
	["PHONE", "telephone"],
	["EMAIL", "email"],
	["DATE", "date"],
	["RADIO", "boolean"],
	// Multi-value matches
	["TEXTBOX_LIST", "text"], // Multi-value text
	["TEXTBOX_LIST", "select"], // Multi-value select
	["MULTIPLE_OPTIONS", "select"], // Multi-value select
	["CHECKBOX", "select"], // Multi-value select
];

// ============================================================================
// COMPATIBILITY CHECKING FUNCTIONS
// ============================================================================

/**
 * Check if two field types are compatible
 */
function areTypesCompatible(
	sourceType: string,
	targetType: string,
	sourcePlatform: Platform
): boolean {
	if (sourcePlatform === Platform.AP) {
		// AP → CC compatibility
		const compatibleTypes = AP_TO_CC_COMPATIBILITY[sourceType];
		return compatibleTypes?.includes(targetType) || false;
	} else {
		// CC → AP compatibility
		const compatibleTypes = CC_TO_AP_COMPATIBILITY[sourceType];
		return compatibleTypes?.includes(targetType) || false;
	}
}

/**
 * Calculate compatibility confidence score
 */
function calculateCompatibilityScore(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform
): number {
	const sourceType = "dataType" in sourceField ? sourceField.dataType : sourceField.type;
	const targetType = "dataType" in targetField ? targetField.dataType : targetField.type;
	
	// Check for exact type match
	const isHighCompatibility = HIGH_COMPATIBILITY_PAIRS.some(([type1, type2]) => {
		return (sourcePlatform === Platform.AP && sourceType === type1 && targetType === type2) ||
			   (sourcePlatform === Platform.CC && sourceType === type2 && targetType === type1);
	});
	
	if (isHighCompatibility) {
		return 0.95; // Very high compatibility
	}
	
	// Check basic compatibility
	if (areTypesCompatible(sourceType, targetType, sourcePlatform)) {
		return 0.80; // Good compatibility
	}
	
	// Check if multi-value handling makes them compatible
	if (sourcePlatform === Platform.CC) {
		const ccField = sourceField as GetCCCustomField;
		const apField = targetField as APGetCustomFieldType;
		
		// CC multi-value → AP multi-value types
		if (isMultiValueField(ccField) && isApMultiValueType(apField.dataType)) {
			return 0.85; // High compatibility for multi-value
		}
		
		// Medical field fallbacks
		if (isMedicalField(ccField.type) && apField.dataType === "TEXT") {
			return 0.75; // Good compatibility for medical fallback
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		const ccField = targetField as GetCCCustomField;
		
		// AP multi-value → CC multi-value
		if (isApMultiValueType(apField.dataType) && isMultiValueField(ccField)) {
			return 0.85; // High compatibility for multi-value
		}
		
		// AP boolean RADIO → CC boolean
		if (isApBooleanRadio(apField) && ccField.type === "boolean") {
			return 0.90; // Very high compatibility for boolean
		}
	}
	
	return 0.0; // No compatibility
}

/**
 * Generate compatibility reason
 */
function generateCompatibilityReason(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform,
	confidence: number
): string {
	const sourceType = "dataType" in sourceField ? sourceField.dataType : sourceField.type;
	const targetType = "dataType" in targetField ? targetField.dataType : targetField.type;
	
	if (confidence === 0.0) {
		return `Incompatible field types: ${sourceType} (${sourcePlatform}) cannot be converted to ${targetType}`;
	}
	
	if (confidence >= 0.90) {
		return `Excellent compatibility: ${sourceType} → ${targetType} with minimal conversion required`;
	}
	
	if (confidence >= 0.80) {
		return `Good compatibility: ${sourceType} → ${targetType} with standard conversion`;
	}
	
	if (confidence >= 0.70) {
		return `Moderate compatibility: ${sourceType} → ${targetType} with specialized conversion`;
	}
	
	return `Low compatibility: ${sourceType} → ${targetType} may require complex conversion`;
}

/**
 * Generate conversion steps for compatible fields
 */
function generateConversionSteps(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform
): string[] {
	const steps: string[] = [];
	const sourceType = "dataType" in sourceField ? sourceField.dataType : sourceField.type;
	const targetType = "dataType" in targetField ? targetField.dataType : targetField.type;
	
	if (sourcePlatform === Platform.CC) {
		const ccField = sourceField as GetCCCustomField;
		const apField = targetField as APGetCustomFieldType;
		
		// CC → AP conversion steps
		if (isMultiValueField(ccField)) {
			if (apField.dataType === "TEXTBOX_LIST") {
				steps.push("Convert CC multi-value to AP TEXTBOX_LIST Record<string, string> format");
			} else if (apField.dataType === "MULTIPLE_OPTIONS") {
				steps.push("Convert CC multi-value select to AP MULTIPLE_OPTIONS");
			} else if (apField.dataType === "TEXT") {
				steps.push("Convert CC multi-value to AP TEXT with pipe separator (fallback)");
			}
		} else if (ccField.type === "boolean" && apField.dataType === "RADIO") {
			steps.push("Convert CC boolean to AP RADIO with Yes/Ja/No/Nein options");
		} else if (isMedicalField(ccField.type) && apField.dataType === "TEXT") {
			steps.push("Convert CC medical field to AP TEXT (fallback conversion)");
		} else {
			steps.push(`Direct conversion from CC ${sourceType} to AP ${targetType}`);
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		const ccField = targetField as GetCCCustomField;
		
		// AP → CC conversion steps
		if (isApMultiValueType(apField.dataType)) {
			if (isMultiValueField(ccField)) {
				steps.push("Convert AP multi-value to CC multi-value format");
			} else {
				steps.push("Convert AP multi-value to CC single-value (data loss possible)");
			}
		} else if (isApBooleanRadio(apField) && ccField.type === "boolean") {
			steps.push("Convert AP boolean RADIO to CC boolean");
		} else {
			steps.push(`Direct conversion from AP ${sourceType} to CC ${targetType}`);
		}
	}
	
	return steps;
}

// ============================================================================
// MAIN COMPATIBILITY FUNCTIONS
// ============================================================================

/**
 * Check field type compatibility between platforms
 * 
 * Validates whether a source field can be converted to a target field type
 * and provides detailed compatibility information including confidence score.
 * 
 * @param sourceField - Source field to check compatibility for
 * @param targetField - Target field to check compatibility against
 * @param sourcePlatform - Platform of the source field
 * @returns Detailed compatibility result
 */
export function checkFieldCompatibility(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform
): CompatibilityResult {
	try {
		const confidence = calculateCompatibilityScore(sourceField, targetField, sourcePlatform);
		const isCompatible = confidence > 0.0;
		const reason = generateCompatibilityReason(sourceField, targetField, sourcePlatform, confidence);
		const conversionSteps = isCompatible 
			? generateConversionSteps(sourceField, targetField, sourcePlatform)
			: [];
		
		return {
			isCompatible,
			confidence,
			reason,
			conversionSteps
		};
		
	} catch (error) {
		return {
			isCompatible: false,
			confidence: 0.0,
			reason: `Compatibility check failed: ${String(error)}`,
			conversionSteps: []
		};
	}
}

/**
 * Check if a field type supports multi-value operations
 */
export function supportsMultiValue(
	field: APGetCustomFieldType | GetCCCustomField,
	platform: Platform
): boolean {
	if (platform === Platform.CC) {
		return isMultiValueField(field as GetCCCustomField);
	} else {
		return isApMultiValueType((field as APGetCustomFieldType).dataType);
	}
}

/**
 * Get compatible field types for a given field
 */
export function getCompatibleFieldTypes(
	fieldType: string,
	sourcePlatform: Platform
): string[] {
	if (sourcePlatform === Platform.AP) {
		return AP_TO_CC_COMPATIBILITY[fieldType] || [];
	} else {
		return CC_TO_AP_COMPATIBILITY[fieldType] || [];
	}
}

/**
 * Check if a field requires value conversion
 */
export function requiresValueConversion(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	sourcePlatform: Platform
): boolean {
	const compatibility = checkFieldCompatibility(sourceField, targetField, sourcePlatform);
	
	// High compatibility usually means minimal conversion
	if (compatibility.confidence >= 0.90) {
		return false;
	}
	
	// Multi-value fields always require conversion
	if (sourcePlatform === Platform.CC) {
		const ccField = sourceField as GetCCCustomField;
		if (isMultiValueField(ccField)) {
			return true;
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		if (isApMultiValueType(apField.dataType)) {
			return true;
		}
	}
	
	// Boolean fields require conversion
	if (sourcePlatform === Platform.CC) {
		const ccField = sourceField as GetCCCustomField;
		if (ccField.type === "boolean") {
			return true;
		}
	} else {
		const apField = sourceField as APGetCustomFieldType;
		if (isApBooleanRadio(apField)) {
			return true;
		}
	}
	
	// Medical fields require conversion
	if (sourcePlatform === Platform.CC) {
		const ccField = sourceField as GetCCCustomField;
		if (isMedicalField(ccField.type)) {
			return true;
		}
	}
	
	return compatibility.confidence < 0.95;
}
