/**
 * Custom Fields v2 Core Module Exports
 * 
 * Central export point for all core custom field functionality including
 * field type conversion and field matching capabilities.
 * 
 * **Exported Functions:**
 * - Field type conversion (bidirectional CC ↔ AP)
 * - Field matching with confidence scoring
 * - Utility functions for field analysis
 * 
 * @fileoverview Core module exports for custom fields v2 system
 * @version 2.0.0
 * @since 2024-08-07
 */

// ============================================================================
// FIELD TYPE CONVERTER EXPORTS
// ============================================================================

export {
	convertFieldType,
	getApFieldTypeForCcField,
	getCcFieldTypeForApField,
	isMultiValueField,
	isMedicalField,
	isApMultiValueType,
	isApBooleanRadio
} from "./fieldTypeConverter";

// ============================================================================
// FIELD MATCHER EXPORTS
// ============================================================================

export {
	findMatchingFields,
	calculateMatchConfidence
} from "./fieldMatcher";

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Re-export core types for convenience
export type {
	ConvertedFieldType,
	FieldMatch,
	FieldMatchOptions,
	FieldMatchConfig,
	FieldMatchStrategy,
	Platform
} from "../types";
