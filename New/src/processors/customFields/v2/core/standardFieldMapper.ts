/**
 * Standard Field Mapper for Custom Fields v2
 *
 * Handles mapping between standard fields and custom fields across platforms.
 * Addresses the critical issue where AP email/phone standard fields need to sync
 * to CC custom fields and vice versa.
 *
 * **Key Features:**
 * - AP standard fields (email, phone) → CC custom fields
 * - CC standard fields (PatientID, CC Profile link) → AP custom fields
 * - Configurable mapping rules with field name patterns
 * - Automatic field creation for missing mappings
 * - Type-aware value conversion
 * - Conflict resolution for duplicate field names
 *
 * **Standard Field Mappings:**
 * - AP patient.email → CC custom field (email type, names: "email", "e-mail", "patient email")
 * - AP patient.phone → CC custom field (telephone type, names: "phone", "telephone", "patient phone")
 * - CC patient data → AP custom fields for CC-specific information
 *
 * @fileoverview Standard to custom field mapping system
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type {
	StandardFieldMapping,
	FieldMapping,
	ConversionResult,
	ValueConversionContext
} from "../types";
import { Platform } from "../types";
import { findMatchingFields } from "./fieldMatcher";
import { convertFieldValue } from "./valueConverter";
import { checkFieldCompatibility } from "./typeChecker";

// ============================================================================
// STANDARD FIELD MAPPING CONFIGURATION
// ============================================================================

/**
 * AP standard field to CC custom field mappings
 */
const AP_STANDARD_TO_CC_CUSTOM: StandardFieldMapping[] = [
	{
		id: "ap_email_to_cc_custom",
		sourceField: "patient.email",
		sourcePlatform: Platform.AP,
		targetPlatform: Platform.CC,
		targetFieldType: "email",
		targetFieldNames: ["email", "e-mail", "patient email", "patient e-mail", "email address"],
		description: "Map AP patient email to CC custom email field",
		priority: 1,
		createIfMissing: true,
		valueConversion: "direct_mapping"
	},
	{
		id: "ap_phone_to_cc_custom",
		sourceField: "patient.phone",
		sourcePlatform: Platform.AP,
		targetPlatform: Platform.CC,
		targetFieldType: "telephone",
		targetFieldNames: ["phone", "telephone", "patient phone", "patient telephone", "phone number"],
		description: "Map AP patient phone to CC custom telephone field",
		priority: 1,
		createIfMissing: true,
		valueConversion: "direct_mapping"
	}
];

/**
 * CC standard field to AP custom field mappings
 */
const CC_STANDARD_TO_AP_CUSTOM: StandardFieldMapping[] = [
	{
		id: "cc_patient_id_to_ap_custom",
		sourceField: "patient.id",
		sourcePlatform: Platform.CC,
		targetPlatform: Platform.AP,
		targetFieldType: "TEXT",
		targetFieldNames: ["cc patient id", "clinicore patient id", "cc id", "patient cc id"],
		description: "Map CC patient ID to AP custom text field",
		priority: 2,
		createIfMissing: true,
		valueConversion: "direct_mapping"
	},
	{
		id: "cc_profile_link_to_ap_custom",
		sourceField: "patient.profileLink",
		sourcePlatform: Platform.CC,
		targetPlatform: Platform.AP,
		targetFieldType: "TEXT",
		targetFieldNames: ["cc profile link", "clinicore profile", "cc patient link", "patient profile url"],
		description: "Map CC patient profile link to AP custom text field",
		priority: 3,
		createIfMissing: true,
		valueConversion: "direct_mapping"
	}
];

/**
 * All standard field mappings combined
 */
const ALL_STANDARD_MAPPINGS = [
	...AP_STANDARD_TO_CC_CUSTOM,
	...CC_STANDARD_TO_AP_CUSTOM
];

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract standard field value from patient data
 */
function extractStandardFieldValue(
	patientData: any,
	fieldPath: string
): any {
	const pathParts = fieldPath.split('.');
	let value = patientData;
	
	for (const part of pathParts) {
		if (value && typeof value === 'object' && part in value) {
			value = value[part];
		} else {
			return undefined;
		}
	}
	
	return value;
}

/**
 * Generate unique field name if conflicts exist
 */
function generateUniqueFieldName(
	baseName: string,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[],
	platform: Platform
): string {
	const existingNames = existingFields.map(field => {
		if (platform === Platform.AP) {
			return (field as APGetCustomFieldType).name;
		} else {
			return (field as GetCCCustomField).name;
		}
	});
	
	let uniqueName = baseName;
	let counter = 1;
	
	while (existingNames.includes(uniqueName)) {
		uniqueName = `${baseName}_${counter}`;
		counter++;
	}
	
	return uniqueName;
}

/**
 * Create field creation payload for missing standard field mapping
 */
function createFieldCreationPayload(
	mapping: StandardFieldMapping,
	existingFields: (APGetCustomFieldType | GetCCCustomField)[]
): any {
	const fieldName = generateUniqueFieldName(
		mapping.targetFieldNames[0],
		existingFields,
		mapping.targetPlatform
	);
	
	if (mapping.targetPlatform === Platform.AP) {
		// Create AP custom field
		return {
			name: fieldName,
			dataType: mapping.targetFieldType,
			isRequired: false,
			description: mapping.description,
			// Add specific options for certain field types
			...(mapping.targetFieldType === "RADIO" && {
				picklistOptions: ["Yes", "Ja", "No", "Nein"]
			})
		};
	} else {
		// Create CC custom field
		return {
			name: fieldName,
			label: fieldName,
			type: mapping.targetFieldType,
			isRequired: false,
			description: mapping.description,
			allowMultipleValues: false
		};
	}
}

// ============================================================================
// STANDARD FIELD MAPPING FUNCTIONS
// ============================================================================

/**
 * Find existing custom field that matches a standard field mapping
 */
export function findMatchingCustomField(
	mapping: StandardFieldMapping,
	customFields: (APGetCustomFieldType | GetCCCustomField)[]
): (APGetCustomFieldType | GetCCCustomField) | null {
	// Try to find exact matches first
	for (const targetName of mapping.targetFieldNames) {
		const exactMatch = customFields.find(field => {
			const fieldName = mapping.targetPlatform === Platform.AP 
				? (field as APGetCustomFieldType).name
				: (field as GetCCCustomField).name;
			return fieldName.toLowerCase() === targetName.toLowerCase();
		});
		
		if (exactMatch) {
			return exactMatch;
		}
	}
	
	// Try fuzzy matching using the field matcher
	// Create a mock source field for matching
	const mockSourceField = mapping.targetPlatform === Platform.AP ? {
		name: mapping.targetFieldNames[0],
		dataType: mapping.targetFieldType,
		id: "mock"
	} as APGetCustomFieldType : {
		name: mapping.targetFieldNames[0],
		type: mapping.targetFieldType,
		id: "mock"
	} as GetCCCustomField;
	
	const matches = findMatchingFields(
		mockSourceField,
		customFields,
		{
			strategy: "normalized" as any,
			fuzzyThreshold: 0.8,
			normalizeGermanChars: true,
			ignoreCase: true,
			ignoreSpaces: true
		},
		{
			minConfidence: 0.8,
			maxResults: 1,
			targetFieldType: mapping.targetFieldType
		}
	);
	
	return matches.length > 0 ? matches[0].field : null;
}

/**
 * Get all standard field mappings for a platform direction
 */
export function getStandardFieldMappings(
	sourcePlatform: Platform,
	targetPlatform: Platform
): StandardFieldMapping[] {
	return ALL_STANDARD_MAPPINGS.filter(mapping => 
		mapping.sourcePlatform === sourcePlatform && 
		mapping.targetPlatform === targetPlatform
	);
}

/**
 * Convert standard field value to custom field format
 */
export function convertStandardFieldValue(
	value: any,
	mapping: StandardFieldMapping,
	targetField?: APGetCustomFieldType | GetCCCustomField
): ConversionResult {
	if (!value || value === "") {
		return {
			success: true,
			convertedValue: null,
			notes: ["Empty standard field value, skipping conversion"]
		};
	}
	
	// Create conversion context
	const context: ValueConversionContext = {
		targetField,
		sourcePlatform: mapping.sourcePlatform,
		targetPlatform: mapping.targetPlatform
	};
	
	// Use the value converter for the conversion
	return convertFieldValue(value, mapping.valueConversion, context);
}

/**
 * Extract standard field values from patient data
 */
export function extractStandardFieldValues(
	patientData: any,
	sourcePlatform: Platform
): Record<string, any> {
	const mappings = getStandardFieldMappings(sourcePlatform, 
		sourcePlatform === Platform.AP ? Platform.CC : Platform.AP);
	
	const extractedValues: Record<string, any> = {};
	
	for (const mapping of mappings) {
		const value = extractStandardFieldValue(patientData, mapping.sourceField);
		if (value !== undefined && value !== null && value !== "") {
			extractedValues[mapping.id] = {
				mapping,
				value
			};
		}
	}
	
	return extractedValues;
}

/**
 * Create missing custom fields for standard field mappings
 */
export function createMissingStandardFields(
	mappings: StandardFieldMapping[],
	existingFields: (APGetCustomFieldType | GetCCCustomField)[]
): any[] {
	const fieldsToCreate: any[] = [];
	
	for (const mapping of mappings) {
		if (!mapping.createIfMissing) continue;
		
		const existingField = findMatchingCustomField(mapping, existingFields);
		if (!existingField) {
			const fieldPayload = createFieldCreationPayload(mapping, existingFields);
			fieldsToCreate.push({
				mapping,
				fieldPayload
			});
		}
	}
	
	return fieldsToCreate;
}

/**
 * Validate standard field mapping compatibility
 */
export function validateStandardFieldMapping(
	mapping: StandardFieldMapping,
	targetField: APGetCustomFieldType | GetCCCustomField
): boolean {
	// Check if the target field type is compatible
	const targetFieldType = mapping.targetPlatform === Platform.AP 
		? (targetField as APGetCustomFieldType).dataType
		: (targetField as GetCCCustomField).type;
	
	// For standard mappings, we expect exact type matches or compatible types
	if (targetFieldType === mapping.targetFieldType) {
		return true;
	}
	
	// Check for compatible types
	const compatibleTypes: Record<string, string[]> = {
		"email": ["TEXT", "text"],
		"telephone": ["PHONE", "TEXT", "text"],
		"TEXT": ["text", "textarea"],
		"text": ["TEXT", "LARGE_TEXT"]
	};
	
	const compatible = compatibleTypes[mapping.targetFieldType];
	return compatible?.includes(targetFieldType) || false;
}

/**
 * Get standard field mapping by ID
 */
export function getStandardFieldMappingById(id: string): StandardFieldMapping | null {
	return ALL_STANDARD_MAPPINGS.find(mapping => mapping.id === id) || null;
}

/**
 * Get all standard field mappings
 */
export function getAllStandardFieldMappings(): StandardFieldMapping[] {
	return [...ALL_STANDARD_MAPPINGS];
}
