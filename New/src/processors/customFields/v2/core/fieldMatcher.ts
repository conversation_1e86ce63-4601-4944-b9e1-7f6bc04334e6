/**
 * Field Matcher for Custom Fields v2
 * 
 * Provides intelligent field matching between AutoPatient (AP) and CliniCore (CC)
 * platforms using multiple matching strategies with confidence scoring.
 * 
 * **Matching Strategies:**
 * - Exact: Direct string comparison of name/label/fieldKey
 * - Normalized: German character normalization, case/space ignoring using matchString
 * - Fuzzy: Similarity-based matching with configurable threshold
 * 
 * **Features:**
 * - Multiple matching strategies with confidence scores
 * - Field type filtering for performance optimization
 * - Configurable matching options and thresholds
 * - Uses existing matchString utility for normalized comparison
 * - Returns ranked results with detailed match metadata
 * 
 * @fileoverview Intelligent field matching system with confidence scoring
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import type {
	FieldMatch,
	FieldMatchConfig,
	FieldMatchOptions
} from "../types";
import { FieldMatchStrategy, Platform } from "../types";
import { matchString } from "../../../../utils/matchString";

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get field name for matching purposes
 * Uses label as primary, falls back to name for CC fields
 * Uses name for AP fields
 */
function getFieldName(field: APGetCustomFieldType | GetCCCustomField): string {
	if ("label" in field) {
		// CC field - use label as primary, fallback to name
		return field.label || field.name || "";
	} else {
		// AP field - use name
		return field.name || "";
	}
}

/**
 * Get field type for filtering
 */
function getFieldType(field: APGetCustomFieldType | GetCCCustomField): string {
	if ("dataType" in field) {
		// AP field
		return field.dataType || "";
	} else {
		// CC field
		return field.type || "";
	}
}

/**
 * Get field ID for exclusion checks
 */
function getFieldId(field: APGetCustomFieldType | GetCCCustomField): string {
	if ("id" in field) {
		return typeof field.id === "string" ? field.id : String(field.id);
	}
	return "";
}

/**
 * Normalize field name for comparison
 * Applies German character normalization and case/space handling
 */
function normalizeFieldName(name: string, config: FieldMatchConfig): string {
	let normalized = name;
	
	// German character normalization
	if (config.normalizeGermanChars) {
		normalized = normalized
			.replace(/ä/g, "a")
			.replace(/ö/g, "o") 
			.replace(/ü/g, "u")
			.replace(/ß/g, "ss")
			.replace(/Ä/g, "A")
			.replace(/Ö/g, "O")
			.replace(/Ü/g, "U");
	}
	
	// Case normalization
	if (config.ignoreCase) {
		normalized = normalized.toLowerCase();
	}
	
	// Space and underscore normalization
	if (config.ignoreSpaces) {
		normalized = normalized.replace(/[\s_-]/g, "");
	}
	
	return normalized;
}

/**
 * Calculate fuzzy similarity between two strings
 * Uses Levenshtein distance algorithm
 */
function calculateSimilarity(str1: string, str2: string): number {
	if (str1 === str2) return 1.0;
	if (str1.length === 0 || str2.length === 0) return 0.0;
	
	const matrix: number[][] = [];
	
	// Initialize matrix
	for (let i = 0; i <= str2.length; i++) {
		matrix[i] = [i];
	}
	for (let j = 0; j <= str1.length; j++) {
		matrix[0][j] = j;
	}
	
	// Fill matrix
	for (let i = 1; i <= str2.length; i++) {
		for (let j = 1; j <= str1.length; j++) {
			if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
				matrix[i][j] = matrix[i - 1][j - 1];
			} else {
				matrix[i][j] = Math.min(
					matrix[i - 1][j - 1] + 1, // substitution
					matrix[i][j - 1] + 1,     // insertion
					matrix[i - 1][j] + 1      // deletion
				);
			}
		}
	}
	
	const maxLength = Math.max(str1.length, str2.length);
	const distance = matrix[str2.length][str1.length];
	
	return (maxLength - distance) / maxLength;
}

// ============================================================================
// MATCHING FUNCTIONS
// ============================================================================

/**
 * Perform exact string matching
 */
function exactMatch(sourceField: APGetCustomFieldType | GetCCCustomField, targetField: APGetCustomFieldType | GetCCCustomField): FieldMatch | null {
	const sourceName = getFieldName(sourceField);
	const targetName = getFieldName(targetField);
	
	// Try exact name match
	if (sourceName === targetName && sourceName.length > 0) {
		return {
			field: targetField,
			confidence: 1.0,
			matchStrategy: FieldMatchStrategy.EXACT,
			matchedOn: "name",
			fieldType: getFieldType(targetField)
		};
	}
	
	// Try fieldKey match for AP fields
	if ("fieldKey" in sourceField && "fieldKey" in targetField) {
		if (sourceField.fieldKey === targetField.fieldKey && sourceField.fieldKey) {
			return {
				field: targetField,
				confidence: 1.0,
				matchStrategy: FieldMatchStrategy.EXACT,
				matchedOn: "fieldKey",
				fieldType: getFieldType(targetField)
			};
		}
	}
	
	return null;
}

/**
 * Perform normalized matching using matchString utility
 */
function normalizedMatch(sourceField: APGetCustomFieldType | GetCCCustomField, targetField: APGetCustomFieldType | GetCCCustomField, config: FieldMatchConfig): FieldMatch | null {
	const sourceName = getFieldName(sourceField);
	const targetName = getFieldName(targetField);
	
	// Use existing matchString utility for normalized comparison
	if (matchString(sourceName, targetName)) {
		return {
			field: targetField,
			confidence: 0.95, // High confidence for normalized match
			matchStrategy: FieldMatchStrategy.NORMALIZED,
			matchedOn: "name",
			fieldType: getFieldType(targetField)
		};
	}
	
	return null;
}

/**
 * Perform fuzzy matching with similarity threshold
 */
function fuzzyMatch(sourceField: APGetCustomFieldType | GetCCCustomField, targetField: APGetCustomFieldType | GetCCCustomField, config: FieldMatchConfig): FieldMatch | null {
	const sourceName = getFieldName(sourceField);
	const targetName = getFieldName(targetField);
	
	if (!sourceName || !targetName) return null;
	
	// Normalize names for fuzzy comparison
	const normalizedSource = normalizeFieldName(sourceName, config);
	const normalizedTarget = normalizeFieldName(targetName, config);
	
	const similarity = calculateSimilarity(normalizedSource, normalizedTarget);
	const threshold = config.fuzzyThreshold || 0.85;
	
	if (similarity >= threshold) {
		return {
			field: targetField,
			confidence: similarity,
			matchStrategy: FieldMatchStrategy.FUZZY,
			matchedOn: "name",
			fieldType: getFieldType(targetField)
		};
	}
	
	return null;
}

/**
 * Calculate match confidence for a specific strategy
 */
export function calculateMatchConfidence(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetField: APGetCustomFieldType | GetCCCustomField,
	strategy: FieldMatchStrategy,
	config: FieldMatchConfig
): number {
	switch (strategy) {
		case FieldMatchStrategy.EXACT:
			const exactResult = exactMatch(sourceField, targetField);
			return exactResult ? exactResult.confidence : 0.0;
			
		case FieldMatchStrategy.NORMALIZED:
			const normalizedResult = normalizedMatch(sourceField, targetField, config);
			return normalizedResult ? normalizedResult.confidence : 0.0;
			
		case FieldMatchStrategy.FUZZY:
			const fuzzyResult = fuzzyMatch(sourceField, targetField, config);
			return fuzzyResult ? fuzzyResult.confidence : 0.0;
			
		default:
			return 0.0;
	}
}

/**
 * Find matching fields using configured strategy
 * 
 * Searches for fields that match the source field using the specified matching
 * strategy and returns ranked results with confidence scores.
 * 
 * @param sourceField - Field to find matches for
 * @param targetFields - Fields to search within
 * @param config - Matching configuration
 * @param options - Additional matching options
 * @returns Array of field matches sorted by confidence (highest first)
 */
export function findMatchingFields(
	sourceField: APGetCustomFieldType | GetCCCustomField,
	targetFields: (APGetCustomFieldType | GetCCCustomField)[],
	config: FieldMatchConfig,
	options: FieldMatchOptions = {}
): FieldMatch[] {
	const matches: FieldMatch[] = [];
	const minConfidence = options.minConfidence || 0.0;
	const maxResults = options.maxResults || 10;
	
	for (const targetField of targetFields) {
		// Apply field type filter if specified
		if (options.targetFieldType && getFieldType(targetField) !== options.targetFieldType) {
			continue;
		}
		
		// Apply field ID exclusion if specified
		if (options.excludeFieldIds?.includes(getFieldId(targetField))) {
			continue;
		}
		
		// Try matching strategies in order of preference
		let match: FieldMatch | null = null;
		
		// 1. Try exact match first
		match = exactMatch(sourceField, targetField);
		if (match && match.confidence >= minConfidence) {
			matches.push(match);
			continue;
		}
		
		// 2. Try normalized match
		if (config.strategy === FieldMatchStrategy.NORMALIZED || config.strategy === FieldMatchStrategy.FUZZY) {
			match = normalizedMatch(sourceField, targetField, config);
			if (match && match.confidence >= minConfidence) {
				matches.push(match);
				continue;
			}
		}
		
		// 3. Try fuzzy match if enabled
		if (config.strategy === FieldMatchStrategy.FUZZY) {
			match = fuzzyMatch(sourceField, targetField, config);
			if (match && match.confidence >= minConfidence) {
				matches.push(match);
			}
		}
	}
	
	// Sort by confidence (highest first) and limit results
	return matches
		.sort((a, b) => b.confidence - a.confidence)
		.slice(0, maxResults);
}
