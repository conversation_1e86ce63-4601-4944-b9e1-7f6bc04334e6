/**
 * Custom Fields v2 Test Handler
 *
 * Test endpoints for demonstrating and validating the v2 custom field
 * synchronization system integration with real field mappings and data.
 *
 * @fileoverview Test handler for v2 custom field synchronization
 * @version 2.0.0
 * @since 2024-08-07
 */

import type { Context } from "hono";
import { CustomFieldSyncV2, FieldMatchStrategy } from "@/processors/customFields/v2/index.js";
import { getAllFieldMappings } from "@/processors/patientCustomFields/fieldMappingResolver.js";
import apiClient from "@/apiClient";
import { logError, logInfo } from "@/utils/logger";

/**
 * Test field synchronization with real data
 * GET /test/cf/sync-fields
 */
export async function testFieldSynchronization(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();
	const requestId = c.get("requestId") || `test-sync-${Date.now()}`;

	try {
		logInfo("Starting test field synchronization", { requestId });

		// Initialize v2 sync system
		const customFieldSync = new CustomFieldSyncV2({
			fieldMatchConfig: {
				strategy: FieldMatchStrategy.NORMALIZED,
				fuzzyThreshold: 0.85,
				normalizeGermanChars: true,
				ignoreCase: true,
				ignoreSpaces: true,
			},
		});

		// Fetch custom fields from both platforms
		const [apFields, ccFields] = await Promise.all([
			apiClient.ap.apCustomfield.allWithParentFilter(true),
			apiClient.cc.ccCustomfieldReq.all(true),
		]);

		logInfo("Fetched custom fields for test", {
			requestId,
			apFieldCount: apFields.length,
			ccFieldCount: ccFields.length,
		});

		// Synchronize field definitions
		const syncResult = await customFieldSync.synchronizeFields(
			apFields,
			ccFields,
			{
				requestId,
				createMissingFields: true,
				includeStandardFields: true,
				logLevel: "INFO",
				dryRun: false, // Set to true for testing without actual changes
			}
		);

		logInfo("Test field synchronization completed", {
			requestId,
			success: syncResult.success,
			matchedFields: syncResult.matchedFields,
			createdFields: syncResult.createdFields,
			skippedFields: syncResult.skippedFields,
			failedFields: syncResult.failedFields,
			processingTimeMs: syncResult.processingTimeMs,
		});

		return c.json({
			status: "success",
			message: "Test field synchronization completed",
			data: {
				requestId,
				timestamp,
				syncResult: {
					success: syncResult.success,
					totalFields: syncResult.totalFields,
					matchedFields: syncResult.matchedFields,
					createdFields: syncResult.createdFields,
					skippedFields: syncResult.skippedFields,
					failedFields: syncResult.failedFields,
					processingTimeMs: syncResult.processingTimeMs,
					warnings: syncResult.warnings,
					errorCount: syncResult.errors.length,
				},
				fieldDetails: {
					apFieldCount: apFields.length,
					ccFieldCount: ccFields.length,
					resultCount: syncResult.results.length,
				},
			},
		});
	} catch (error) {
		logError("Test field synchronization failed", {
			requestId,
			error: String(error),
		});

		return c.json(
			{
				status: "error",
				message: "Test field synchronization failed",
				data: {
					requestId,
					timestamp,
					error: String(error),
				},
			},
			500,
		);
	}
}

/**
 * Test patient value synchronization with real mappings
 * POST /test/cf/sync-patient-values
 */
export async function testPatientValueSynchronization(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();
	const requestId = c.get("requestId") || `test-patient-${Date.now()}`;

	try {
		// Get request body with patient data
		const body = await c.req.json();
		const { patientData, platform } = body;

		if (!patientData) {
			return c.json(
				{
					status: "error",
					message: "Patient data is required",
					data: { requestId, timestamp },
				},
				400,
			);
		}

		logInfo("Starting test patient value synchronization", {
			requestId,
			platform,
			patientDataKeys: Object.keys(patientData),
		});

		// Initialize v2 sync system
		const customFieldSync = new CustomFieldSyncV2();

		// Get existing field mappings
		const fieldMappings = await getAllFieldMappings();

		logInfo("Retrieved field mappings for test", {
			requestId,
			mappingCount: fieldMappings.length,
		});

		// Convert field mappings to v2 format
		const v2FieldMappings = fieldMappings.map(mapping => ({
			id: `mapping-${mapping.apId}-${mapping.ccId}`,
			apFieldId: mapping.apId,
			ccFieldId: mapping.ccId,
			apFieldName: mapping.apConfig.name,
			ccFieldName: mapping.ccConfig.label || mapping.ccConfig.name,
			apFieldType: mapping.apConfig.dataType as any,
			ccFieldType: mapping.ccConfig.type as any,
			matchStrategy: FieldMatchStrategy.EXACT,
			confidence: 1.0,
			isStandardField: false,
			isActive: true,
		}));

		// Synchronize patient values
		const syncResult = await customFieldSync.synchronizePatientValues(
			{ ...patientData, platform },
			v2FieldMappings,
			{
				requestId,
				dryRun: true, // Always dry run for testing
				logLevel: "INFO",
			}
		);

		logInfo("Test patient value synchronization completed", {
			requestId,
			patientId: syncResult.patientId,
			platform: syncResult.platform,
			successfulUpdates: syncResult.successfulUpdates,
			failedUpdates: syncResult.failedUpdates,
			processingTimeMs: syncResult.processingTimeMs,
		});

		return c.json({
			status: "success",
			message: "Test patient value synchronization completed",
			data: {
				requestId,
				timestamp,
				syncResult: {
					patientId: syncResult.patientId,
					platform: syncResult.platform,
					totalFields: syncResult.totalFields,
					processedFields: syncResult.processedFields,
					successfulUpdates: syncResult.successfulUpdates,
					failedUpdates: syncResult.failedUpdates,
					skippedFields: syncResult.skippedFields,
					processingTimeMs: syncResult.processingTimeMs,
					errors: syncResult.errors,
					warnings: syncResult.warnings,
				},
				mappingDetails: {
					totalMappings: fieldMappings.length,
					v2MappingCount: v2FieldMappings.length,
				},
			},
		});
	} catch (error) {
		logError("Test patient value synchronization failed", {
			requestId,
			error: String(error),
		});

		return c.json(
			{
				status: "error",
				message: "Test patient value synchronization failed",
				data: {
					requestId,
					timestamp,
					error: String(error),
				},
			},
			500,
		);
	}
}

/**
 * Get current field mappings in v2 format
 * GET /test/cf/field-mappings
 */
export async function getFieldMappingsV2Format(c: Context): Promise<Response> {
	const timestamp = new Date().toISOString();
	const requestId = c.get("requestId") || `test-mappings-${Date.now()}`;

	try {
		logInfo("Retrieving field mappings in v2 format", { requestId });

		// Get existing field mappings
		const fieldMappings = await getAllFieldMappings();

		// Convert to v2 format
		const v2FieldMappings = fieldMappings.map(mapping => ({
			id: `mapping-${mapping.apId}-${mapping.ccId}`,
			apFieldId: mapping.apId,
			ccFieldId: mapping.ccId,
			apFieldName: mapping.apConfig.name,
			ccFieldName: mapping.ccConfig.label || mapping.ccConfig.name,
			apFieldType: mapping.apConfig.dataType,
			ccFieldType: mapping.ccConfig.type,
			matchStrategy: FieldMatchStrategy.EXACT,
			confidence: 1.0,
			isStandardField: false,
			mappingType: mapping.mappingType,
			isActive: true,
		}));

		logInfo("Retrieved and converted field mappings", {
			requestId,
			originalCount: fieldMappings.length,
			v2Count: v2FieldMappings.length,
		});

		return c.json({
			status: "success",
			message: "Field mappings retrieved successfully",
			data: {
				requestId,
				timestamp,
				mappings: {
					original: fieldMappings,
					v2Format: v2FieldMappings,
				},
				statistics: {
					totalMappings: fieldMappings.length,
					customToCustomMappings: fieldMappings.filter(m => m.mappingType === "custom_to_custom").length,
					standardMappings: fieldMappings.filter(m => m.mappingType !== "custom_to_custom").length,
				},
			},
		});
	} catch (error) {
		logError("Failed to retrieve field mappings", {
			requestId,
			error: String(error),
		});

		return c.json(
			{
				status: "error",
				message: "Failed to retrieve field mappings",
				data: {
					requestId,
					timestamp,
					error: String(error),
				},
			},
			500,
		);
	}
}
