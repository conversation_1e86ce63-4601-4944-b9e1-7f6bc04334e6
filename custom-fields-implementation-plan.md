# Custom Fields System Implementation Plan

## 🔍 **CURRENT STATE ANALYSIS**

### **Existing Systems**
1. **V3Integration (Legacy)**: Working but limited custom field sync in `/v3Integration/`
2. **New System (Incomplete)**: Documented but not implemented, handlers expecting v2 system
3. **Database Schema**: Ready for new system with field mappings table
4. **Documentation**: Comprehensive (`custom-field-sync.md`, `DATA-TYPE-MAP.md`)

### **Critical Issues**
- Email/phone standard fields not syncing to custom fields
- TEXTBOX_LIST handling inconsistencies
- Missing pipe separator for multi-value conversions
- No real type checking in field matching

## 🎯 **IMPLEMENTATION STRATEGY**

### **Phase 1: Core Infrastructure (Priority 1)**

#### **Directory Structure**
```
New/src/processors/customFields/v2/
├── core/
│   ├── fieldMatcher.ts          # Intelligent field matching
│   ├── valueConverter.ts        # Unified value conversion
│   ├── standardFieldMapper.ts   # Standard↔Custom mapping
│   └── typeChecker.ts          # Type compatibility validation
├── sync/
│   ├── fieldDefinitionSync.ts  # Field creation/mapping sync
│   ├── fieldValueSync.ts       # Patient value synchronization
│   └── standardFieldSync.ts    # Standard field sync
├── converters/
│   ├── apToCC.ts               # AP→CC conversion
│   ├── ccToAP.ts               # CC→AP conversion
│   └── textboxList.ts          # TEXTBOX_LIST handling
├── types/
│   └── index.ts                # Type definitions
├── config/
│   ├── fieldMappings.ts        # Field type mappings
│   └── standardMappings.ts     # Standard field rules
└── index.ts                    # Main entry point
```

#### **Key Classes Required**
1. **CustomFieldSyncV2** - Main orchestrator class
2. **FieldValueSync** - Patient value synchronization
3. **FieldMatchStrategy** - Enum for matching strategies

### **Phase 2: Core Components (Priority 1)**

#### **Field Matcher (`core/fieldMatcher.ts`)**
- **Exact matching**: name/label/fieldKey comparison
- **Normalized matching**: German umlauts, spaces, special chars
- **Real type compatibility**: Based on DATA-TYPE-MAP.md rules

#### **Value Converter (`core/valueConverter.ts`)**
- **Unified conversion**: Single function for all field types
- **TEXTBOX_LIST handling**: Uses `field_value` object with option IDs as keys (Record<string, string>)
- **Multi-value to TEXT**: Join with ` | ` separator ONLY when CC multi-value maps to AP TEXT field
- **Type-aware**: Respect source/target field types and allowMultipleValues flag

#### **Type Checker (`core/typeChecker.ts`)**
- **Real validation**: Based on DATA-TYPE-MAP.md compatibility matrix
- **Multi-value support**: allowMultipleValues handling
- **TEXTBOX_LIST compatibility**: Proper CC field type checking

#### **Standard Field Mapper (`core/standardFieldMapper.ts`)**
- **AP standard→CC custom**: email, phone mapping
- **CC standard→AP custom**: PatientID, CC Profile link
- **Configurable**: Easy to add new mappings

### **Phase 3: Synchronization Engines (Priority 2)**

#### **Field Definition Sync (`sync/fieldDefinitionSync.ts`)**
1. Fetch all AP and CC custom fields
2. Match fields using intelligent algorithms
3. Create missing fields (CC→AP only, per rules)
4. Store mappings in database
5. Handle conflicts and duplicates

#### **Field Value Sync (`sync/fieldValueSync.ts`)**
1. Fetch patient data from source platform
2. Get field mappings from database
3. Convert values using unified converter
4. Handle TEXTBOX_LIST special cases
5. Update target platform with converted values

#### **Standard Field Sync (`sync/standardFieldSync.ts`)**
1. Extract standard fields from patient data
2. Map to target custom fields using configuration
3. Convert values with proper type handling
4. Integrate with regular custom field sync

### **Phase 4: Platform Converters (Priority 2)**

#### **AP→CC Converter (`converters/apToCC.ts`)**
- **TEXTBOX_LIST → CC multi-value**: Convert Record<string, string> to CC format
- **AP TEXT → CC multi-value**: Split by ` | ` separator when target is multi-value
- **RADIO → boolean**: "Yes"/"Ja" → `true`, "No"/"Nein" → `false`
- **Standard field types**: TEXT→text, NUMERICAL→number, EMAIL→email, etc.

#### **CC→AP Converter (`converters/ccToAP.ts`)**
- **CC multi-value select → MULTIPLE_OPTIONS**: Preserve select-based structure (preferred for select fields)
- **CC multi-value (other) → TEXTBOX_LIST**: Preserve Record<string, string> structure (preferred)
- **CC multi-value → TEXT**: Join with ` | ` separator (fallback when no TEXTBOX_LIST match)
- **boolean → RADIO**: `true`→"Yes", `false`→"No", create options ["Yes", "Ja", "No", "Nein"]
- **Medical fields → TEXT**: medication, permanent-diagnoses, patient-has-recommended fallbacks

#### **TEXTBOX_LIST Handler (`converters/textboxList.ts`)**
- **Value Storage**: Uses `field_value` object with option IDs as keys
- **Option ID Management**: Fetch picklistOptions, map values to option IDs
- **Complete Replacement**: Replace old values entirely (not append)
- **Fresh Data Fetching**: Always fetch fresh CC data instead of cached

### **Phase 5: Integration & API (Priority 1)**

#### **Main Entry Point (`index.ts`)**
```typescript
export class CustomFieldSyncV2 {
  constructor(config: FieldMatchConfig)
  async synchronizeFields(apFields, ccFields, options): Promise<SyncResult>
  async synchronizePatientValues(patientData, fieldMappings, options): Promise<SyncResult>
}

export class FieldValueSync {
  async synchronizePatientValues(context: ValueSyncContext): Promise<SyncSummary>
}

export enum FieldMatchStrategy {
  EXACT = "exact",
  NORMALIZED = "normalized", 
  FUZZY = "fuzzy"
}
```

## 🔧 **TECHNICAL REQUIREMENTS**

### **Field Creation Rules (from DATA-TYPE-MAP.md)**
- **AP → CC**: Never create new CC fields, only map to existing ones
- **CC → AP**: Create new AP fields if no mapping exists
- **Multi-value Detection**:
  - CC `select` fields with `allowMultipleValues: true` → AP MULTIPLE_OPTIONS (preferred), fallback to TEXTBOX_LIST
  - All other CC fields with `allowMultipleValues: true` → AP TEXTBOX_LIST (preferred)
- **Fallback Mapping**: If no TEXTBOX_LIST match, CC multi-value → AP TEXT with ` | ` separator
- **Conflicts**: Generate unique field names with suffix (e.g., "email_2")
- **Skipped Fields**: FILE_UPLOAD fields are always skipped
- **Medical Field Fallbacks**: medication, permanent-diagnoses, patient-has-recommended → TEXT

### **Value Conversion Logic (from DATA-TYPE-MAP.md)**
- **TEXTBOX_LIST Storage**: Uses `field_value` object with option IDs as keys (Record<string, string>)
- **TEXTBOX_LIST Sync**: Completely replaces old values (not append), fetch fresh CC data
- **CC Multi-value → AP TEXTBOX_LIST**: Preserve Record<string, string> structure (preferred path)
- **CC Multi-value → AP TEXT**: Join with ` | ` separator (fallback when no TEXTBOX_LIST match)
- **AP TEXT → CC Multi-value**: Split by ` | ` separator and sync back to CC
- **Boolean Conversion**: CC `true`→"Yes", `false`→"No", AP RADIO options: ["Yes", "Ja", "No", "Nein"]

### **API Interface Compatibility**
Must match existing handler expectations:
- `CustomFieldSyncV2` constructor with fieldMatchConfig
- `synchronizeFields()` method returning specific result format
- `synchronizePatientValues()` method for patient data sync
- `FieldValueSync` class for admin endpoints

### **Data Type Mappings**
Implement complete mapping matrix from DATA-TYPE-MAP.md:
- **AP TEXTBOX_LIST ↔ CC multi-value**: Record<string, string> structure with option IDs
- **CC multi-value → AP TEXT**: Join with ` | ` separator (fallback when no TEXTBOX_LIST match)
- **AP RADIO ↔ CC boolean**: `true`→"Yes", `false`→"No", options: ["Yes", "Ja", "No", "Nein"]
- **Medical field fallbacks**: `medication`, `permanent-diagnoses`, `patient-has-recommended` → TEXT

### **Error Handling**
- Comprehensive error handling for all operations
- Detailed logging for debugging and monitoring
- Graceful fallbacks for missing fields
- Validation of field compatibility before conversion

### **Performance Considerations**
- Efficient field matching algorithms
- Batch processing for multiple fields
- Caching of field mappings
- Minimal API calls through smart batching

## 🚀 **IMPLEMENTATION PRIORITY**

### **Immediate (Week 1)**
1. Create directory structure
2. Implement type definitions
3. Build CustomFieldSyncV2 and FieldValueSync classes
4. Fix import errors in handlers

### **Core Features (Week 1-2)**
1. Implement field matcher with real type checking
2. Build unified value converter
3. Create standard field mapper
4. Add TEXTBOX_LIST specialized handling

### **Integration (Week 2)**
1. Implement synchronization engines
2. Add platform-specific converters
3. Create configuration files
4. Add comprehensive error handling

### **Testing & Migration (Week 3)**
1. Test with existing handlers
2. Implement feature flag system
3. Create migration documentation
4. Performance optimization

## 🎯 **SUCCESS CRITERIA**

### **Functional Requirements**
- [ ] Handlers can import v2 system without errors
- [ ] AP email/phone syncs to CC custom fields
- [ ] TEXTBOX_LIST uses Record<string, string> with option IDs (not comma-separated)
- [ ] CC multi-value → AP TEXT uses ` | ` separator (fallback only)
- [ ] CC multi-value → AP TEXTBOX_LIST preserves object structure (preferred)
- [ ] Field matching has real type validation
- [ ] Standard field mappings work seamlessly
- [ ] Medical field types (medication, permanent-diagnoses) fallback to TEXT

### **Technical Requirements**
- [ ] 100% API compatibility with handler expectations
- [ ] Complete DATA-TYPE-MAP.md implementation
- [ ] Comprehensive error handling and logging
- [ ] Performance equal or better than v3Integration
- [ ] Clean, maintainable code architecture

## 🎯 **CURRENT STATUS**

### **✅ COMPLETED (Phase 1)**
- [x] Created directory structure (`New/src/processors/customFields/v2/`)
- [x] Implemented type definitions (`types/index.ts`)
- [x] Created main entry point (`index.ts`) with placeholder classes
- [x] Fixed import errors in handlers
- [x] Created `getAllFieldMappings` function
- [x] Implemented placeholder `synchronizeCustomFields` function
- [x] Created patient custom fields processor with placeholder functions

### **🔄 IN PROGRESS**
- [ ] Core component implementation (field matcher, value converter, type checker)
- [ ] Synchronization engines (field definition sync, value sync)
- [ ] Platform-specific converters (AP↔CC)
- [ ] Configuration files with DATA-TYPE-MAP.md mappings

### **⏳ PENDING**
- [ ] Real field matching algorithms
- [ ] Value conversion with proper separators
- [ ] TEXTBOX_LIST specialized handling
- [ ] Standard field mapping implementation
- [ ] Database integration for field mappings
- [ ] Error handling and logging
- [ ] Feature flag system for migration
- [ ] Comprehensive testing

## 📋 **NEXT STEPS**

### **Immediate Priority (Week 1)**
1. **Implement Core Components**:
   - Build `FieldMatcher` with exact, normalized, and fuzzy matching
   - Create `ValueConverter` with TEXTBOX_LIST Record<string, string> and ` | ` separator for multi-value→TEXT fallback
   - Implement `TypeChecker` with real compatibility validation
   - Add `StandardFieldMapper` for standard↔custom mappings

2. **Create Configuration Files**:
   - Implement field type mappings from DATA-TYPE-MAP.md
   - Add standard field mapping rules
   - Create field match strategy configurations

3. **Build Synchronization Engines**:
   - Implement `FieldDefinitionSync` for field creation/mapping
   - Create `FieldValueSync` for patient value synchronization
   - Add `StandardFieldSync` for standard field handling

### **Integration Priority (Week 2)**
1. **Platform Converters**:
   - Build AP→CC conversion with TEXTBOX_LIST handling
   - Create CC→AP conversion with multi-value support
   - Implement specialized TEXTBOX_LIST converter

2. **Database Integration**:
   - Connect field mapping resolver to database
   - Implement CRUD operations for field mappings
   - Add field mapping validation and conflict resolution

3. **Real Implementation**:
   - Replace placeholder functions with actual logic
   - Integrate with existing API clients
   - Add comprehensive error handling

### **Testing & Migration (Week 3)**
1. **Testing**:
   - Unit tests for core components
   - Integration tests with real data
   - Performance testing and optimization

2. **Migration Strategy**:
   - Implement feature flag system
   - Create migration utilities
   - Document rollout plan

## 🚨 **CRITICAL ISSUES RESOLVED**

### **✅ Import Errors Fixed**
- All handlers can now import v2 system classes without errors
- `CustomFieldSyncV2` and `FieldValueSync` classes are available
- `getAllFieldMappings` function is implemented
- `synchronizeCustomFields` function is available

### **✅ Architecture Foundation**
- Complete type system with proper interfaces
- Modular directory structure following documentation
- Placeholder implementations maintain API compatibility
- Database schema is ready for field mappings

## 🔧 **TECHNICAL DEBT**

### **Placeholder Implementations**
All current implementations are placeholders that return success with warnings.
Real functionality needs to be implemented in the following order:

1. **Core Components** (highest priority)
2. **Synchronization Engines** (medium priority)
3. **Platform Converters** (medium priority)
4. **Advanced Features** (lower priority)

### **Missing Dependencies**
- Real field matching algorithms
- Value conversion logic with type awareness
- API integration for field CRUD operations
- Error handling and retry mechanisms

---

**Status: Foundation complete, ready for core implementation. All import errors resolved, handlers functional with placeholder responses.**
